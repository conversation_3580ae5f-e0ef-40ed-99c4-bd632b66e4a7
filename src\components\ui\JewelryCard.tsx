"use client";

import React from "react";
import { motion } from "framer-motion";
import { JewelryItem } from "@/types";
import { Button } from "./Button";

interface JewelryCardProps {
  item: JewelryItem;
  onViewDetails?: (item: JewelryItem) => void;
}

export const JewelryCard: React.FC<JewelryCardProps> = ({
  item,
  onViewDetails,
}) => {
  return (
    <motion.div
      className="group relative bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:border-[#D4AF37] transition-all duration-300"
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      {/* Image Container */}
      <div className="relative h-64 overflow-hidden">
        <motion.img
          src={item.images[0] || "/images/placeholder-jewelry.svg"}
          alt={item.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/images/placeholder-jewelry.svg";
          }}
        />

        {/* Overlay on Hover */}
        <motion.div
          className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
        >
          <Button
            onClick={() => onViewDetails?.(item)}
            variant="primary"
            size="sm"
            className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300"
          >
            View Details
          </Button>
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="font-playfair text-xl font-semibold text-black mb-2 group-hover:text-[#D4AF37] transition-colors duration-300">
          {item.title}
        </h3>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {item.description}
        </p>

        <div className="flex justify-between items-center">
          {item.price && (
            <span className="text-[#D4AF37] font-semibold text-lg">
              {item.price}
            </span>
          )}

          <div className="text-right">
            <p className="text-xs text-gray-500">{item.category}</p>
            {item.purity && (
              <p className="text-xs text-gray-500">{item.purity}</p>
            )}
          </div>
        </div>
      </div>

      {/* Gold accent line */}
      <div className="absolute bottom-0 left-0 w-0 h-1 bg-[#D4AF37] group-hover:w-full transition-all duration-500" />
    </motion.div>
  );
};
