import { Vendor, JewelryItem } from '@/types';

export const vendors: Vendor[] = [
  {
    id: 'vendor-1',
    name: 'Golden Jewelry',
    phone: '******-0123',
    location: 'Paris, France',
    logo: '/images/vendors/golden-jewelry-logo.jpg',
    description: 'Luxury handcrafted jewelry since 1985'
  },
  {
    id: 'vendor-2',
    name: 'Lux Gold',
    phone: '******-0124',
    location: 'Dubai, UAE',
    logo: '/images/vendors/lux-gold-logo.jpg',
    description: 'Premium gold and diamond specialists'
  },
  {
    id: 'vendor-3',
    name: 'Jewels & Co.',
    phone: '******-0125',
    location: 'India',
    logo: '/images/vendors/jewels-co-logo.jpg',
    description: 'Traditional and modern jewelry designs'
  }
];

export const jewelryItems: JewelryItem[] = [
  {
    id: 'item-1',
    title: '18k Gold Necklace',
    description: 'Elegant handcrafted necklace with intricate design patterns',
    price: '$1,500',
    images: [
      '/images/jewelry/necklace-1-main.jpg',
      '/images/jewelry/necklace-1-detail.jpg'
    ],
    category: 'Necklace',
    weight: '25g',
    purity: '18k',
    vendor_id: 'vendor-1'
  },
  {
    id: 'item-2',
    title: '18k Gold Ring',
    description: 'Classic gold ring with traditional craftsmanship',
    price: '$1,200',
    images: [
      '/images/jewelry/ring-1-main.jpg',
      '/images/jewelry/ring-1-detail.jpg'
    ],
    category: 'Ring',
    weight: '15g',
    purity: '18k',
    vendor_id: 'vendor-2'
  },
  {
    id: 'item-3',
    title: '18k Gold Bracelet',
    description: 'Luxurious chain bracelet with premium finish',
    price: '$2,300',
    images: [
      '/images/jewelry/bracelet-1-main.jpg',
      '/images/jewelry/bracelet-1-detail.jpg'
    ],
    category: 'Bracelet',
    weight: '35g',
    purity: '18k',
    vendor_id: 'vendor-3'
  },
  {
    id: 'item-4',
    title: 'Diamond Gold Pendant',
    description: 'Exquisite pendant with embedded diamonds',
    price: '$3,500',
    images: [
      '/images/jewelry/pendant-1-main.jpg',
      '/images/jewelry/pendant-1-detail.jpg'
    ],
    category: 'Pendant',
    weight: '20g',
    purity: '18k',
    vendor_id: 'vendor-1'
  },
  {
    id: 'item-5',
    title: 'Gold Chain Bracelet',
    description: 'Sophisticated chain design with premium gold',
    price: '$1,800',
    images: [
      '/images/jewelry/chain-bracelet-1-main.jpg',
      '/images/jewelry/chain-bracelet-1-detail.jpg'
    ],
    category: 'Bracelet',
    weight: '28g',
    purity: '18k',
    vendor_id: 'vendor-2'
  },
  {
    id: 'item-6',
    title: 'Gold Drop Earrings',
    description: 'Elegant drop earrings with intricate detailing',
    price: '$2,100',
    images: [
      '/images/jewelry/earrings-1-main.jpg',
      '/images/jewelry/earrings-1-detail.jpg'
    ],
    category: 'Earrings',
    weight: '18g',
    purity: '18k',
    vendor_id: 'vendor-3'
  },
  {
    id: 'item-7',
    title: 'Vintage Gold Ring',
    description: 'Classic vintage-style ring with ornate patterns',
    price: '$2,800',
    images: [
      '/images/jewelry/vintage-ring-1-main.jpg',
      '/images/jewelry/vintage-ring-1-detail.jpg'
    ],
    category: 'Ring',
    weight: '22g',
    purity: '18k',
    vendor_id: 'vendor-1'
  },
  {
    id: 'item-8',
    title: 'Gold Bangle Set',
    description: 'Set of three matching gold bangles',
    price: '$4,200',
    images: [
      '/images/jewelry/bangle-set-1-main.jpg',
      '/images/jewelry/bangle-set-1-detail.jpg'
    ],
    category: 'Bracelet',
    weight: '45g',
    purity: '18k',
    vendor_id: 'vendor-2'
  }
];

export const categories = [
  { id: 'necklace', name: 'Necklaces' },
  { id: 'ring', name: 'Rings' },
  { id: 'bracelet', name: 'Bracelets' },
  { id: 'earrings', name: 'Earrings' },
  { id: 'pendant', name: 'Pendants' },
  { id: 'chain', name: 'Chains' }
];
