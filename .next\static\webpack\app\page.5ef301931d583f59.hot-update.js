"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/FeaturedCollections.tsx":
/*!*********************************************************!*\
  !*** ./src/components/sections/FeaturedCollections.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedCollections: () => (/* binding */ FeaturedCollections)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_JewelryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/JewelryCard */ \"(app-pages-browser)/./src/components/ui/JewelryCard.tsx\");\n/* harmony import */ var _data_sampleData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/sampleData */ \"(app-pages-browser)/./src/data/sampleData.ts\");\n/* __next_internal_client_entry_do_not_use__ FeaturedCollections auto */ \n\n\n\n\nconst FeaturedCollections = ()=>{\n    // Get first 6 items for featured collections\n    const featuredItems = _data_sampleData__WEBPACK_IMPORTED_MODULE_3__.jewelryItems.slice(0, 6);\n    const handleViewDetails = (item)=>{\n        // For now, just log the item. Later this can navigate to item details page\n        console.log(\"View details for:\", item.title);\n    // TODO: Navigate to item details page\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"collections\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-playfair text-4xl md:text-5xl font-bold text-black mb-4\",\n                            children: \"Explore Our Collections\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-[#D4AF37] mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg max-w-2xl mx-auto\",\n                            children: \"Discover our carefully curated selection of exquisite gold and jewelry pieces, each crafted with precision and passion by our trusted artisans.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: featuredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_JewelryCard__WEBPACK_IMPORTED_MODULE_2__.JewelryCard, {\n                                item: item,\n                                onViewDetails: handleViewDetails\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 15\n                            }, undefined)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mt-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-transparent border-2 border-gold text-gold hover:bg-gold hover:text-black px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg\",\n                        children: \"VIEW ALL COLLECTIONS\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\FeaturedCollections.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FeaturedCollections;\nvar _c;\n$RefreshReg$(_c, \"FeaturedCollections\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL0ZlYXR1cmVkQ29sbGVjdGlvbnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDbUI7QUFDVDtBQUcxQyxNQUFNSSxzQkFBZ0M7SUFDM0MsNkNBQTZDO0lBQzdDLE1BQU1DLGdCQUFnQkYsMERBQVlBLENBQUNHLEtBQUssQ0FBQyxHQUFHO0lBRTVDLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QiwyRUFBMkU7UUFDM0VDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJGLEtBQUtHLEtBQUs7SUFDM0Msc0NBQXNDO0lBQ3hDO0lBRUEsTUFBTUMsb0JBQW9CO1FBQ3hCQyxRQUFRO1lBQUVDLFNBQVM7UUFBRTtRQUNyQkMsU0FBUztZQUNQRCxTQUFTO1lBQ1RFLFlBQVk7Z0JBQ1ZDLGlCQUFpQjtZQUNuQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxlQUFlO1FBQ25CTCxRQUFRO1lBQUVDLFNBQVM7WUFBR0ssR0FBRztRQUFHO1FBQzVCSixTQUFTO1lBQ1BELFNBQVM7WUFDVEssR0FBRztZQUNISCxZQUFZO2dCQUNWSSxVQUFVO2dCQUNWQyxNQUFNO1lBQ1I7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQVFDLElBQUc7UUFBY0MsV0FBVTtrQkFDbEMsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUViLDhEQUFDdkIsaURBQU1BLENBQUN3QixHQUFHO29CQUNURCxXQUFVO29CQUNWRSxTQUFTO3dCQUFFWixTQUFTO3dCQUFHSyxHQUFHO29CQUFHO29CQUM3QlEsYUFBYTt3QkFBRWIsU0FBUzt3QkFBR0ssR0FBRztvQkFBRTtvQkFDaENILFlBQVk7d0JBQUVJLFVBQVU7b0JBQUk7b0JBQzVCUSxVQUFVO3dCQUFFQyxNQUFNO29CQUFLOztzQ0FFdkIsOERBQUNDOzRCQUFHTixXQUFVO3NDQUErRDs7Ozs7O3NDQUc3RSw4REFBQ0M7NEJBQUlELFdBQVU7Ozs7OztzQ0FDZiw4REFBQ087NEJBQUVQLFdBQVU7c0NBQTBDOzs7Ozs7Ozs7Ozs7OEJBUXpELDhEQUFDdkIsaURBQU1BLENBQUN3QixHQUFHO29CQUNURCxXQUFVO29CQUNWUSxVQUFVcEI7b0JBQ1ZjLFNBQVE7b0JBQ1JDLGFBQVk7b0JBQ1pDLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7OEJBRXRCeEIsY0FBYzRCLEdBQUcsQ0FBQyxDQUFDekIscUJBQ2xCLDhEQUFDUCxpREFBTUEsQ0FBQ3dCLEdBQUc7NEJBQWVPLFVBQVVkO3NDQUNsQyw0RUFBQ2hCLG1FQUFXQTtnQ0FBQ00sTUFBTUE7Z0NBQU0wQixlQUFlM0I7Ozs7OzsyQkFEekJDLEtBQUtlLEVBQUU7Ozs7Ozs7Ozs7OEJBTzVCLDhEQUFDdEIsaURBQU1BLENBQUN3QixHQUFHO29CQUNURCxXQUFVO29CQUNWRSxTQUFTO3dCQUFFWixTQUFTO3dCQUFHSyxHQUFHO29CQUFHO29CQUM3QlEsYUFBYTt3QkFBRWIsU0FBUzt3QkFBR0ssR0FBRztvQkFBRTtvQkFDaENILFlBQVk7d0JBQUVJLFVBQVU7d0JBQUtlLE9BQU87b0JBQUk7b0JBQ3hDUCxVQUFVO3dCQUFFQyxNQUFNO29CQUFLOzhCQUV2Qiw0RUFBQ087d0JBQU9aLFdBQVU7a0NBQThKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzFMLEVBQUU7S0FwRldwQiIsInNvdXJjZXMiOlsiRDpcXGRldlxccHJvamVjdHNcXGdvbGQtbWFya2V0XFxzcmNcXGNvbXBvbmVudHNcXHNlY3Rpb25zXFxGZWF0dXJlZENvbGxlY3Rpb25zLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSBcImZyYW1lci1tb3Rpb25cIjtcbmltcG9ydCB7IEpld2VscnlDYXJkIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9KZXdlbHJ5Q2FyZFwiO1xuaW1wb3J0IHsgamV3ZWxyeUl0ZW1zIH0gZnJvbSBcIkAvZGF0YS9zYW1wbGVEYXRhXCI7XG5pbXBvcnQgeyBKZXdlbHJ5SXRlbSB9IGZyb20gXCJAL3R5cGVzXCI7XG5cbmV4cG9ydCBjb25zdCBGZWF0dXJlZENvbGxlY3Rpb25zOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgLy8gR2V0IGZpcnN0IDYgaXRlbXMgZm9yIGZlYXR1cmVkIGNvbGxlY3Rpb25zXG4gIGNvbnN0IGZlYXR1cmVkSXRlbXMgPSBqZXdlbHJ5SXRlbXMuc2xpY2UoMCwgNik7XG5cbiAgY29uc3QgaGFuZGxlVmlld0RldGFpbHMgPSAoaXRlbTogSmV3ZWxyeUl0ZW0pID0+IHtcbiAgICAvLyBGb3Igbm93LCBqdXN0IGxvZyB0aGUgaXRlbS4gTGF0ZXIgdGhpcyBjYW4gbmF2aWdhdGUgdG8gaXRlbSBkZXRhaWxzIHBhZ2VcbiAgICBjb25zb2xlLmxvZyhcIlZpZXcgZGV0YWlscyBmb3I6XCIsIGl0ZW0udGl0bGUpO1xuICAgIC8vIFRPRE86IE5hdmlnYXRlIHRvIGl0ZW0gZGV0YWlscyBwYWdlXG4gIH07XG5cbiAgY29uc3QgY29udGFpbmVyVmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7IG9wYWNpdHk6IDAgfSxcbiAgICB2aXNpYmxlOiB7XG4gICAgICBvcGFjaXR5OiAxLFxuICAgICAgdHJhbnNpdGlvbjoge1xuICAgICAgICBzdGFnZ2VyQ2hpbGRyZW46IDAuMixcbiAgICAgIH0sXG4gICAgfSxcbiAgfTtcblxuICBjb25zdCBpdGVtVmFyaWFudHMgPSB7XG4gICAgaGlkZGVuOiB7IG9wYWNpdHk6IDAsIHk6IDUwIH0sXG4gICAgdmlzaWJsZToge1xuICAgICAgb3BhY2l0eTogMSxcbiAgICAgIHk6IDAsXG4gICAgICB0cmFuc2l0aW9uOiB7XG4gICAgICAgIGR1cmF0aW9uOiAwLjYsXG4gICAgICAgIGVhc2U6IFwiZWFzZU91dFwiLFxuICAgICAgfSxcbiAgICB9LFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gaWQ9XCJjb2xsZWN0aW9uc1wiIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICB7LyogU2VjdGlvbiBIZWFkZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTZcIlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICA+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtcGxheWZhaXIgdGV4dC00eGwgbWQ6dGV4dC01eGwgZm9udC1ib2xkIHRleHQtYmxhY2sgbWItNFwiPlxuICAgICAgICAgICAgRXhwbG9yZSBPdXIgQ29sbGVjdGlvbnNcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTEgYmctWyNENEFGMzddIG14LWF1dG8gbWItNlwiIC8+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LWxnIG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICBEaXNjb3ZlciBvdXIgY2FyZWZ1bGx5IGN1cmF0ZWQgc2VsZWN0aW9uIG9mIGV4cXVpc2l0ZSBnb2xkIGFuZFxuICAgICAgICAgICAgamV3ZWxyeSBwaWVjZXMsIGVhY2ggY3JhZnRlZCB3aXRoIHByZWNpc2lvbiBhbmQgcGFzc2lvbiBieSBvdXJcbiAgICAgICAgICAgIHRydXN0ZWQgYXJ0aXNhbnMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIENvbGxlY3Rpb25zIEdyaWQgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiXG4gICAgICAgICAgdmFyaWFudHM9e2NvbnRhaW5lclZhcmlhbnRzfVxuICAgICAgICAgIGluaXRpYWw9XCJoaWRkZW5cIlxuICAgICAgICAgIHdoaWxlSW5WaWV3PVwidmlzaWJsZVwiXG4gICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICA+XG4gICAgICAgICAge2ZlYXR1cmVkSXRlbXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdiBrZXk9e2l0ZW0uaWR9IHZhcmlhbnRzPXtpdGVtVmFyaWFudHN9PlxuICAgICAgICAgICAgICA8SmV3ZWxyeUNhcmQgaXRlbT17aXRlbX0gb25WaWV3RGV0YWlscz17aGFuZGxlVmlld0RldGFpbHN9IC8+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICB7LyogVmlldyBBbGwgQnV0dG9uICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG10LTEyXCJcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCwgZGVsYXk6IDAuNCB9fVxuICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYmctdHJhbnNwYXJlbnQgYm9yZGVyLTIgYm9yZGVyLWdvbGQgdGV4dC1nb2xkIGhvdmVyOmJnLWdvbGQgaG92ZXI6dGV4dC1ibGFjayBweC04IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3ZlcjpzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIFZJRVcgQUxMIENPTExFQ1RJT05TXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJtb3Rpb24iLCJKZXdlbHJ5Q2FyZCIsImpld2VscnlJdGVtcyIsIkZlYXR1cmVkQ29sbGVjdGlvbnMiLCJmZWF0dXJlZEl0ZW1zIiwic2xpY2UiLCJoYW5kbGVWaWV3RGV0YWlscyIsIml0ZW0iLCJjb25zb2xlIiwibG9nIiwidGl0bGUiLCJjb250YWluZXJWYXJpYW50cyIsImhpZGRlbiIsIm9wYWNpdHkiLCJ2aXNpYmxlIiwidHJhbnNpdGlvbiIsInN0YWdnZXJDaGlsZHJlbiIsIml0ZW1WYXJpYW50cyIsInkiLCJkdXJhdGlvbiIsImVhc2UiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJkaXYiLCJpbml0aWFsIiwid2hpbGVJblZpZXciLCJ2aWV3cG9ydCIsIm9uY2UiLCJoMiIsInAiLCJ2YXJpYW50cyIsIm1hcCIsIm9uVmlld0RldGFpbHMiLCJkZWxheSIsImJ1dHRvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/FeaturedCollections.tsx\n"));

/***/ })

});