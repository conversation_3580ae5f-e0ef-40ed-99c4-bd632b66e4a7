"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n\n\n\nconst Button = (param)=>{\n    let { children, onClick, variant = \"primary\", size = \"md\", className = \"\", disabled = false } = param;\n    const baseClasses = \"font-medium rounded-lg transition-all duration-300 cursor-pointer inline-flex items-center justify-center\";\n    const variantClasses = {\n        primary: \"bg-[#D4AF37] text-black hover:bg-[#B8941F] shadow-lg hover:shadow-xl\",\n        secondary: \"bg-black text-white hover:bg-gray-800\",\n        outline: \"border-2 border-[#D4AF37] text-[#D4AF37] hover:bg-[#D4AF37] hover:text-black\"\n    };\n    const sizeClasses = {\n        sm: \"px-4 py-2 text-sm\",\n        md: \"px-6 py-3 text-base\",\n        lg: \"px-8 py-4 text-lg\"\n    };\n    const classes = \"\".concat(baseClasses, \" \").concat(variantClasses[variant], \" \").concat(sizeClasses[size], \" \").concat(className, \" \").concat(disabled ? \"opacity-50 cursor-not-allowed\" : \"\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n        className: classes,\n        onClick: onClick,\n        disabled: disabled,\n        whileHover: {\n            scale: disabled ? 1 : 1.05\n        },\n        whileTap: {\n            scale: disabled ? 1 : 0.95\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBQ2E7QUFXaEMsTUFBTUUsU0FBZ0M7UUFBQyxFQUM1Q0MsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLFVBQVUsU0FBUyxFQUNuQkMsT0FBTyxJQUFJLEVBQ1hDLFlBQVksRUFBRSxFQUNkQyxXQUFXLEtBQUssRUFDakI7SUFDQyxNQUFNQyxjQUNKO0lBRUYsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUNFO1FBQ0ZDLFdBQVc7UUFDWEMsU0FDRTtJQUNKO0lBRUEsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLE1BQU1DLFVBQVUsR0FBa0JSLE9BQWZELGFBQVksS0FDN0JLLE9BRGdDSixjQUFjLENBQUNMLFFBQVEsRUFBQyxLQUV0REUsT0FERk8sV0FBVyxDQUFDUixLQUFLLEVBQ2xCLEtBQWdCRSxPQUFiRCxXQUFVLEtBQW1ELE9BQWhEQyxXQUFXLGtDQUFrQztJQUU5RCxxQkFDRSw4REFBQ1AsaURBQU1BLENBQUNrQixNQUFNO1FBQ1paLFdBQVdXO1FBQ1hkLFNBQVNBO1FBQ1RJLFVBQVVBO1FBQ1ZZLFlBQVk7WUFBRUMsT0FBT2IsV0FBVyxJQUFJO1FBQUs7UUFDekNjLFVBQVU7WUFBRUQsT0FBT2IsV0FBVyxJQUFJO1FBQUs7UUFDdkNlLFlBQVk7WUFBRUMsVUFBVTtRQUFJO2tCQUUzQnJCOzs7Ozs7QUFHUCxFQUFFO0tBekNXRCIsInNvdXJjZXMiOlsiRDpcXGRldlxccHJvamVjdHNcXGdvbGQtbWFya2V0XFxzcmNcXGNvbXBvbmVudHNcXHVpXFxCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gXCJmcmFtZXItbW90aW9uXCI7XG5cbmludGVyZmFjZSBCdXR0b25Qcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xuICB2YXJpYW50PzogXCJwcmltYXJ5XCIgfCBcInNlY29uZGFyeVwiIHwgXCJvdXRsaW5lXCI7XG4gIHNpemU/OiBcInNtXCIgfCBcIm1kXCIgfCBcImxnXCI7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgY29uc3QgQnV0dG9uOiBSZWFjdC5GQzxCdXR0b25Qcm9wcz4gPSAoe1xuICBjaGlsZHJlbixcbiAgb25DbGljayxcbiAgdmFyaWFudCA9IFwicHJpbWFyeVwiLFxuICBzaXplID0gXCJtZFwiLFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBkaXNhYmxlZCA9IGZhbHNlLFxufSkgPT4ge1xuICBjb25zdCBiYXNlQ2xhc3NlcyA9XG4gICAgXCJmb250LW1lZGl1bSByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBjdXJzb3ItcG9pbnRlciBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIjtcblxuICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IHtcbiAgICBwcmltYXJ5OlxuICAgICAgXCJiZy1bI0Q0QUYzN10gdGV4dC1ibGFjayBob3ZlcjpiZy1bI0I4OTQxRl0gc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bFwiLFxuICAgIHNlY29uZGFyeTogXCJiZy1ibGFjayB0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktODAwXCIsXG4gICAgb3V0bGluZTpcbiAgICAgIFwiYm9yZGVyLTIgYm9yZGVyLVsjRDRBRjM3XSB0ZXh0LVsjRDRBRjM3XSBob3ZlcjpiZy1bI0Q0QUYzN10gaG92ZXI6dGV4dC1ibGFja1wiLFxuICB9O1xuXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiBcInB4LTQgcHktMiB0ZXh0LXNtXCIsXG4gICAgbWQ6IFwicHgtNiBweS0zIHRleHQtYmFzZVwiLFxuICAgIGxnOiBcInB4LTggcHktNCB0ZXh0LWxnXCIsXG4gIH07XG5cbiAgY29uc3QgY2xhc3NlcyA9IGAke2Jhc2VDbGFzc2VzfSAke3ZhcmlhbnRDbGFzc2VzW3ZhcmlhbnRdfSAke1xuICAgIHNpemVDbGFzc2VzW3NpemVdXG4gIH0gJHtjbGFzc05hbWV9ICR7ZGlzYWJsZWQgPyBcIm9wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkXCIgOiBcIlwifWA7XG5cbiAgcmV0dXJuIChcbiAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgY2xhc3NOYW1lPXtjbGFzc2VzfVxuICAgICAgb25DbGljaz17b25DbGlja31cbiAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IGRpc2FibGVkID8gMSA6IDEuMDUgfX1cbiAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiBkaXNhYmxlZCA/IDEgOiAwLjk1IH19XG4gICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjIgfX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9tb3Rpb24uYnV0dG9uPlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIm1vdGlvbiIsIkJ1dHRvbiIsImNoaWxkcmVuIiwib25DbGljayIsInZhcmlhbnQiLCJzaXplIiwiY2xhc3NOYW1lIiwiZGlzYWJsZWQiLCJiYXNlQ2xhc3NlcyIsInZhcmlhbnRDbGFzc2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsIm91dGxpbmUiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImNsYXNzZXMiLCJidXR0b24iLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJ3aGlsZVRhcCIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Button.tsx\n"));

/***/ })

});