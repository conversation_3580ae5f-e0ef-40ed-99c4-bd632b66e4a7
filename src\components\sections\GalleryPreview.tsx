"use client";

import React from "react";
import { motion } from "framer-motion";
import { jewelryItems } from "@/data/sampleData";

export const GalleryPreview: React.FC = () => {
  // Get 8 items for gallery preview
  const galleryItems = jewelryItems.slice(0, 8);

  const handleItemClick = (itemId: string) => {
    console.log("Navigate to item:", itemId);
    // TODO: Navigate to full exhibition page or item details
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="font-playfair text-4xl md:text-5xl font-bold text-black mb-4">
            Discover Masterpieces
          </h2>
          <div className="w-24 h-1 bg-gold mx-auto mb-6" />
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Explore our curated gallery of exceptional jewelry pieces, each
            telling its own story of craftsmanship, beauty, and timeless
            elegance.
          </p>
        </motion.div>

        {/* Masonry Grid */}
        <div className="columns-1 md:columns-2 lg:columns-4 gap-6 space-y-6">
          {galleryItems.map((item, index) => (
            <motion.div
              key={item.id}
              className="break-inside-avoid group cursor-pointer"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              onClick={() => handleItemClick(item.id)}
            >
              <div className="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                <motion.img
                  src={item.images[0] || "/images/placeholder-jewelry.svg"}
                  alt={item.title}
                  className="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-110"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/images/placeholder-jewelry.svg";
                  }}
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                />

                {/* Overlay */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />

                {/* Text Overlay */}
                <motion.div
                  className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"
                  initial={{ y: "100%" }}
                  whileHover={{ y: 0 }}
                >
                  <h3 className="font-playfair text-lg font-semibold mb-1">
                    {item.title}
                  </h3>
                  {item.price && (
                    <p className="text-gold font-medium">{item.price}</p>
                  )}
                </motion.div>

                {/* Gold accent corner */}
                <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* View Full Gallery Button */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="bg-black text-white hover:bg-gold hover:text-black px-10 py-4 rounded-lg font-semibold text-lg transition-all duration-300 hover:shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => console.log("Navigate to full gallery")}
          >
            VIEW FULL GALLERY
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};
