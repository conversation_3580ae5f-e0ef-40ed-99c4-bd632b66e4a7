'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Vendor } from '@/types';
import { MapPin, Phone } from 'lucide-react';

interface VendorCardProps {
  vendor: Vendor;
  onViewProfile?: (vendor: Vendor) => void;
}

export const VendorCard: React.FC<VendorCardProps> = ({ vendor, onViewProfile }) => {
  return (
    <motion.div
      className="group bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-gold min-w-[280px]"
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      {/* Vendor Logo/Avatar */}
      <div className="relative mb-4">
        <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-gold-light to-gold flex items-center justify-center overflow-hidden">
          {vendor.logo ? (
            <img
              src={vendor.logo}
              alt={vendor.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          ) : (
            <span className="text-2xl font-playfair font-bold text-black">
              {vendor.name.charAt(0)}
            </span>
          )}
        </div>
        
        {/* Gold ring around avatar on hover */}
        <motion.div
          className="absolute inset-0 w-20 h-20 mx-auto rounded-full border-2 border-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ scale: 0.8 }}
          whileHover={{ scale: 1.1 }}
        />
      </div>

      {/* Vendor Info */}
      <h3 className="font-playfair text-xl font-semibold text-black mb-2 group-hover:text-gold transition-colors duration-300">
        {vendor.name}
      </h3>

      <div className="flex items-center justify-center text-gray-600 mb-2">
        <MapPin size={16} className="mr-1" />
        <span className="text-sm">{vendor.location}</span>
      </div>

      <div className="flex items-center justify-center text-gray-600 mb-4">
        <Phone size={16} className="mr-1" />
        <span className="text-sm">{vendor.phone}</span>
      </div>

      {vendor.description && (
        <p className="text-gray-500 text-sm mb-4 line-clamp-2">
          {vendor.description}
        </p>
      )}

      {/* View Profile Button */}
      <motion.button
        onClick={() => onViewProfile?.(vendor)}
        className="text-gold hover:text-black hover:bg-gold px-4 py-2 rounded-lg border border-gold transition-all duration-300 text-sm font-medium"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        View Profile
      </motion.button>

      {/* Gold underline effect */}
      <motion.div
        className="w-0 h-0.5 bg-gold mx-auto mt-4 group-hover:w-full transition-all duration-500"
        initial={{ width: 0 }}
        whileHover={{ width: '100%' }}
      />
    </motion.div>
  );
};
