export interface Vendor {
  id: string;
  name: string;
  phone: string;
  location: string;
  logo?: string;
  description?: string;
}

export interface JewelryItem {
  id: string;
  title: string;
  description: string;
  price?: string;
  images: string[];
  category: string;
  weight?: string;
  purity?: string;
  vendor_id: string;
}

export interface Category {
  id: string;
  name: string;
}

export type JewelryCategory = 'Necklace' | 'Ring' | 'Bracelet' | 'Earrings' | 'Pendant' | 'Chain';
