'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/Button';

export const CallToActionBanner: React.FC = () => {
  const handleMeetVendors = () => {
    console.log('Navigate to vendors directory');
    // TODO: Navigate to vendors directory page
  };

  return (
    <section className="relative py-20 bg-black overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 border border-gold rounded-full" />
        <div className="absolute top-32 right-20 w-24 h-24 border border-gold rounded-full" />
        <div className="absolute bottom-20 left-1/4 w-16 h-16 border border-gold rounded-full" />
        <div className="absolute bottom-32 right-1/3 w-20 h-20 border border-gold rounded-full" />
        
        {/* Decorative lines */}
        <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-gold to-transparent" />
        <div className="absolute top-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-gold to-transparent opacity-50" />
        <div className="absolute bottom-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-gold to-transparent opacity-50" />
      </div>

      {/* Animated background elements */}
      <motion.div
        className="absolute top-20 left-1/4 w-2 h-2 bg-gold rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-24 right-1/4 w-3 h-3 bg-gold rounded-full"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Main Message */}
          <motion.h2
            className="font-playfair text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            See something you{' '}
            <span className="text-gold">like?</span>
          </motion.h2>

          <motion.p
            className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Contact vendors directly and own your masterpiece.
            <br />
            <span className="text-gold font-medium">Your dream jewelry awaits.</span>
          </motion.p>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <Button
              onClick={handleMeetVendors}
              size="lg"
              className="text-lg px-12 py-4 font-semibold tracking-wide"
            >
              MEET OUR VENDORS
            </Button>
          </motion.div>

          {/* Additional Info */}
          <motion.div
            className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-white">
              <div className="text-gold text-2xl font-bold mb-2">50+</div>
              <div className="text-gray-300">Exquisite Pieces</div>
            </div>
            <div className="text-white">
              <div className="text-gold text-2xl font-bold mb-2">3</div>
              <div className="text-gray-300">Trusted Vendors</div>
            </div>
            <div className="text-white">
              <div className="text-gold text-2xl font-bold mb-2">100%</div>
              <div className="text-gray-300">Authentic Gold</div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Bottom gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black to-transparent" />
    </section>
  );
};
