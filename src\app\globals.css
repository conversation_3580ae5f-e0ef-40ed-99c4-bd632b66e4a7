@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";

:root {
  --gold: #d4af37;
  --black: #0a0a0a;
  --white: #ffffff;
  --gold-light: #f4e4bc;
  --gold-dark: #b8941f;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  color: var(--black);
  background-color: var(--white);
  line-height: 1.6;
}

.font-playfair {
  font-family: "Playfair Display", serif;
}

.text-gold {
  color: var(--gold);
}

.bg-gold {
  background-color: var(--gold);
}

.border-gold {
  border-color: var(--gold);
}

.bg-black {
  background-color: var(--black);
}

.text-black {
  color: var(--black);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gold-dark);
}
