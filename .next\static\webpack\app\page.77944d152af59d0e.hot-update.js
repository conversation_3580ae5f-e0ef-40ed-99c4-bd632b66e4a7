"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \n\n\n\nconst HeroSection = ()=>{\n    const scrollToCollections = ()=>{\n        const collectionsSection = document.getElementById(\"collections\");\n        if (collectionsSection) {\n            collectionsSection.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full bg-cover bg-center bg-no-repeat\",\n                        style: {\n                            backgroundImage: \"linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/images/hero-jewelry-bg.svg')\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black opacity-90\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center text-white px-4 max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                        className: \"font-playfair text-5xl md:text-7xl lg:text-8xl font-bold mb-6 leading-tight\",\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.2\n                        },\n                        children: [\n                            \"Discover\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#D4AF37]\",\n                                children: \"Exquisite\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Gold & Jewelry\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                        className: \"text-xl md:text-2xl lg:text-3xl font-light mb-8 text-gray-200\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 0.6\n                        },\n                        children: \"An exclusive exhibition of timeless elegance\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 1,\n                            delay: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: scrollToCollections,\n                            size: \"lg\",\n                            className: \"text-lg px-10 py-4 font-semibold tracking-wide\",\n                            children: \"EXPLORE COLLECTIONS\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute top-20 left-10 w-2 h-2 bg-[#D4AF37] rounded-full opacity-60\",\n                animate: {\n                    scale: [\n                        1,\n                        1.5,\n                        1\n                    ],\n                    opacity: [\n                        0.6,\n                        1,\n                        0.6\n                    ]\n                },\n                transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute bottom-32 right-16 w-3 h-3 bg-[#D4AF37] rounded-full opacity-40\",\n                animate: {\n                    scale: [\n                        1,\n                        1.2,\n                        1\n                    ],\n                    opacity: [\n                        0.4,\n                        0.8,\n                        0.4\n                    ]\n                },\n                transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"w-6 h-10 border-2 border-[#D4AF37] rounded-full flex justify-center\",\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"w-1 h-3 bg-[#D4AF37] rounded-full mt-2\",\n                        animate: {\n                            opacity: [\n                                1,\n                                0,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dev\\\\projects\\\\gold-market\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection.tsx\n"));

/***/ })

});