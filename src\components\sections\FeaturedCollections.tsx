"use client";

import React from "react";
import { motion } from "framer-motion";
import { JewelryCard } from "@/components/ui/JewelryCard";
import { jewelryItems } from "@/data/sampleData";
import { JewelryItem } from "@/types";

export const FeaturedCollections: React.FC = () => {
  // Get first 6 items for featured collections
  const featuredItems = jewelryItems.slice(0, 6);

  const handleViewDetails = (item: JewelryItem) => {
    // For now, just log the item. Later this can navigate to item details page
    console.log("View details for:", item.title);
    // TODO: Navigate to item details page
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="collections" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="font-playfair text-4xl md:text-5xl font-bold text-black mb-4">
            Explore Our Collections
          </h2>
          <div className="w-24 h-1 bg-[#D4AF37] mx-auto mb-6" />
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Discover our carefully curated selection of exquisite gold and
            jewelry pieces, each crafted with precision and passion by our
            trusted artisans.
          </p>
        </motion.div>

        {/* Collections Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {featuredItems.map((item) => (
            <motion.div key={item.id} variants={itemVariants}>
              <JewelryCard item={item} onViewDetails={handleViewDetails} />
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <button className="bg-transparent border-2 border-[#D4AF37] text-[#D4AF37] hover:bg-[#D4AF37] hover:text-black px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg">
            VIEW ALL COLLECTIONS
          </button>
        </motion.div>
      </div>
    </section>
  );
};
